<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\StaffLedger;
use App\Models\ChequeStatement;
use App\Models\AccountSubGroup;
use App\Models\Payment;
use App\Http\Controllers\StatementController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class StatementControllerDeclinedChequeTest extends TestCase
{
    use RefreshDatabase;

    protected $statementController;

    protected function setUp(): void
    {
        parent::setUp();
        $this->statementController = new StatementController();
    }

    /** @test */
    public function test_declined_cheque_for_customer_sales_shows_in_debit()
    {
        // Create a customer
        $customer = Customer::create([
            'customer_name' => 'Test Customer',
            'phone' => '**********',
            'openingbalance' => 0
        ]);

        // Create a payment record first
        $payment = Payment::create([
            'voucher_no' => 'REC-001',
            'transaction_id' => 1,
            'transaction_type' => 'sale',
            'reference_no' => 'SALE-001',
            'refer_type' => 'Customer',
            'refer_id' => $customer->id,
            'refer_name' => $customer->customer_name,
            'amount' => 1000.00,
            'discount' => 0.00,
            'payment_date' => Carbon::now(),
            'payment_method' => 'Cheque',
            'cheque_no' => 'CHQ001',
            'bank_name' => 'Test Bank',
            'issue_date' => Carbon::now(),
        ]);

        // Create a declined cheque for sales
        ChequeStatement::create([
            'payment_id' => $payment->id,
            'voucher_no' => 'REC-001',
            'transaction_id' => 1,
            'transaction_type' => 'sale',
            'reference_no' => 'SALE-001',
            'refer_type' => 'Customer',
            'refer_id' => $customer->id,
            'refer_name' => $customer->customer_name,
            'amount' => 1000.00,
            'payment_date' => Carbon::now(),
            'cheque_no' => 'CHQ001',
            'bank_name' => 'Test Bank',
            'issue_date' => Carbon::now(),
            'status' => 'declined'
        ]);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->statementController);
        $method = $reflection->getMethod('getDeclinedChequeTransactions');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->statementController,
            'Customer',
            $customer->id,
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        $this->assertCount(1, $result);
        $this->assertEquals(1000.00, $result[0]['debit']);
        $this->assertEquals(0, $result[0]['credit']);
        $this->assertStringContainsString('Declined Cheque - Sale', $result[0]['type']);
    }

    /** @test */
    public function test_declined_cheque_for_supplier_purchase_shows_in_credit()
    {
        // Create a supplier
        $supplier = Supplier::create([
            'supplier_name' => 'Test Supplier',
            'contact' => '0987654321',
            'address' => 'Test Address',
            'opening_balance' => 0
        ]);

        // Create a payment record first
        $payment = Payment::create([
            'voucher_no' => 'PAY-001',
            'transaction_id' => 1,
            'transaction_type' => 'purchase',
            'reference_no' => 'PUR-001',
            'refer_type' => 'Supplier',
            'refer_id' => $supplier->id,
            'refer_name' => $supplier->supplier_name,
            'amount' => 1500.00,
            'discount' => 0.00,
            'payment_date' => Carbon::now(),
            'payment_method' => 'Cheque',
            'cheque_no' => 'CHQ002',
            'bank_name' => 'Test Bank',
            'issue_date' => Carbon::now(),
        ]);

        // Create a declined cheque for purchase
        ChequeStatement::create([
            'payment_id' => $payment->id,
            'voucher_no' => 'PAY-001',
            'transaction_id' => 1,
            'transaction_type' => 'purchase',
            'reference_no' => 'PUR-001',
            'refer_type' => 'Supplier',
            'refer_id' => $supplier->id,
            'refer_name' => $supplier->supplier_name,
            'amount' => 1500.00,
            'payment_date' => Carbon::now(),
            'cheque_no' => 'CHQ002',
            'bank_name' => 'Test Bank',
            'issue_date' => Carbon::now(),
            'status' => 'declined'
        ]);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->statementController);
        $method = $reflection->getMethod('getDeclinedChequeTransactions');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->statementController,
            'Supplier',
            $supplier->id,
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        $this->assertCount(1, $result);
        $this->assertEquals(0, $result[0]['debit']);
        $this->assertEquals(1500.00, $result[0]['credit']);
        $this->assertStringContainsString('Declined Cheque - Purchase', $result[0]['type']);
    }

    /** @test */
    public function test_declined_cheque_for_bank_account_payment_voucher_shows_in_credit()
    {
        // Create account sub group for bank accounts
        AccountSubGroup::create([
            'sub_group_name' => 'Bank Accounts',
            'main_group' => 'Bank Accounts'
        ]);

        // Create a bank account ledger
        $bankLedger = StaffLedger::create([
            'staff_id' => 1,
            'name' => 'Test Bank Account',
            'account_group' => 'Bank Accounts',
            'opening_balance' => 0
        ]);

        // Create a payment record first
        $payment = Payment::create([
            'voucher_no' => 'PAY-001',
            'transaction_id' => null,
            'transaction_type' => 'opening_balance',
            'reference_no' => 'PAY-001',
            'refer_type' => 'Ledger',
            'refer_id' => $bankLedger->id,
            'refer_name' => $bankLedger->name,
            'amount' => 2000.00,
            'discount' => 0.00,
            'payment_date' => Carbon::now(),
            'payment_method' => 'Cheque',
            'cheque_no' => 'CHQ003',
            'bank_name' => 'Test Bank Account',
            'issue_date' => Carbon::now(),
        ]);

        // Create a declined cheque for payment voucher
        ChequeStatement::create([
            'payment_id' => $payment->id,
            'voucher_no' => 'PAY-001',
            'transaction_id' => null,
            'transaction_type' => 'opening_balance',
            'reference_no' => 'PAY-001',
            'refer_type' => 'Ledger',
            'refer_id' => $bankLedger->id,
            'refer_name' => $bankLedger->name,
            'amount' => 2000.00,
            'payment_date' => Carbon::now(),
            'cheque_no' => 'CHQ003',
            'bank_name' => 'Test Bank Account',
            'issue_date' => Carbon::now(),
            'status' => 'declined'
        ]);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->statementController);
        $method = $reflection->getMethod('getDeclinedChequeTransactions');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->statementController,
            'Other',
            $bankLedger->staff_id,
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        $this->assertCount(1, $result);
        $this->assertEquals(0, $result[0]['debit']);
        $this->assertEquals(2000.00, $result[0]['credit']);
        $this->assertStringContainsString('Declined Cheque - Payment Voucher', $result[0]['type']);
    }

    /** @test */
    public function test_declined_cheque_for_bank_account_receive_voucher_shows_in_debit()
    {
        // Create account sub group for bank accounts
        AccountSubGroup::create([
            'sub_group_name' => 'Bank Accounts',
            'main_group' => 'Bank Accounts'
        ]);

        // Create a bank account ledger
        $bankLedger = StaffLedger::create([
            'staff_id' => 2,
            'name' => 'Test Bank Account 2',
            'account_group' => 'Bank Accounts',
            'opening_balance' => 0
        ]);

        // Create a payment record first
        $payment = Payment::create([
            'voucher_no' => 'REC-001',
            'transaction_id' => null,
            'transaction_type' => 'opening_balance',
            'reference_no' => 'REC-001',
            'refer_type' => 'Ledger',
            'refer_id' => $bankLedger->id,
            'refer_name' => $bankLedger->name,
            'amount' => 3000.00,
            'discount' => 0.00,
            'payment_date' => Carbon::now(),
            'payment_method' => 'Cheque',
            'cheque_no' => 'CHQ004',
            'bank_name' => 'Test Bank Account 2',
            'issue_date' => Carbon::now(),
        ]);

        // Create a declined cheque for receive voucher
        ChequeStatement::create([
            'payment_id' => $payment->id,
            'voucher_no' => 'REC-001',
            'transaction_id' => null,
            'transaction_type' => 'opening_balance',
            'reference_no' => 'REC-001',
            'refer_type' => 'Ledger',
            'refer_id' => $bankLedger->id,
            'refer_name' => $bankLedger->name,
            'amount' => 3000.00,
            'payment_date' => Carbon::now(),
            'cheque_no' => 'CHQ004',
            'bank_name' => 'Test Bank Account 2',
            'issue_date' => Carbon::now(),
            'status' => 'declined'
        ]);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->statementController);
        $method = $reflection->getMethod('getDeclinedChequeTransactions');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->statementController,
            'Other',
            $bankLedger->staff_id,
            Carbon::now()->startOfDay(),
            Carbon::now()->endOfDay()
        );

        $this->assertCount(1, $result);
        $this->assertEquals(3000.00, $result[0]['debit']);
        $this->assertEquals(0, $result[0]['credit']);
        $this->assertStringContainsString('Declined Cheque - Receive Voucher', $result[0]['type']);
    }
}
